#!/usr/bin/env python3
"""
Shared Type Definitions for JIRA Ticket Processing

This module contains strongly-typed data structures shared between
jira_similarity_matrix.py and idea_refiner.py to improve type safety
and reduce Dict[str, Any] usage throughout the codebase.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set, Union, Any, TypedDict
from pathlib import Path
from enum import Enum


class ChangeType(Enum):
    """File change types from git."""
    MODIFIED = "M"
    ADDED = "A"
    DELETED = "D"
    RENAMED = "R"


class PaymentScheme(Enum):
    """Payment processing schemes."""
    VISA = "visa"
    MASTERCARD = "mc"
    AMEX = "amex"
    JCB = "jcb"
    DINERS = "diners"


@dataclass
class FileChange:
    """Represents a file change in a git commit."""
    file_path: str
    change_type: ChangeType
    
    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'FileChange':
        """Create FileChange from dictionary representation."""
        return cls(
            file_path=data["file_path"],
            change_type=ChangeType(data["change_type"])
        )
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary representation."""
        return {
            "file_path": self.file_path,
            "change_type": self.change_type.value
        }


@dataclass
class JiraTicket:
    """Core JIRA ticket structure."""
    key: str
    title: str
    description: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'JiraTicket':
        """Create JiraTicket from dictionary representation."""
        return cls(
            key=data["key"],
            title=data["title"],
            description=data["description"]
        )
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary representation."""
        return {
            "key": self.key,
            "title": self.title,
            "description": self.description
        }


@dataclass
class EnrichedJiraTicket:
    """JIRA ticket with file change information."""
    key: str
    title: str
    description: str
    files_changed: List[FileChange] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnrichedJiraTicket':
        """Create EnrichedJiraTicket from dictionary representation."""
        files_changed = []
        if "files_changed" in data:
            files_changed = [
                FileChange.from_dict(fc) if isinstance(fc, dict) else fc
                for fc in data["files_changed"]
            ]
        
        return cls(
            key=data["key"],
            title=data["title"],
            description=data["description"],
            files_changed=files_changed
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "key": self.key,
            "title": self.title,
            "description": self.description,
            "files_changed": [fc.to_dict() for fc in self.files_changed]
        }
    
    def to_base_ticket(self) -> JiraTicket:
        """Convert to base JiraTicket."""
        return JiraTicket(
            key=self.key,
            title=self.title,
            description=self.description
        )


@dataclass
class MLXConfig:
    """Configuration for MLX model usage."""
    model_preset: str
    model_path: str
    max_context: int
    temperature: float = 0.1
    max_tokens: int = 200
    repetition_penalty: float = 1.05
    repetition_context_size: int = 256


# MLX Model presets configuration
MLX_PRESETS: Dict[str, MLXConfig] = {
    "gemma3": MLXConfig(
        model_preset="gemma3",
        model_path="/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
        max_context=8192
    ),
    "qwen3": MLXConfig(
        model_preset="qwen3", 
        model_path="/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
        max_context=32768
    ),
}


@dataclass
class ValidationResult:
    """Result of validation operations."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class LLMResponseDict(TypedDict):
    """Type definition for LLM JSON responses."""
    similarity_score: float
    reasoning: Optional[str]


class RefinementResponseDict(TypedDict, total=False):
    """Type definition for idea refinement LLM responses."""
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str


@dataclass
class ContextMetadata:
    """Metadata about context used in LLM processing."""
    similar_tickets_count: int = 0
    scheme_patterns_used: List[PaymentScheme] = field(default_factory=list)
    file_patterns_count: int = 0
    architectural_components_used: List[str] = field(default_factory=list)
    total_context_tokens: int = 0
    context_truncated: bool = False


@dataclass
class SimilarityMatrixMetadata:
    """Metadata for similarity matrix generation."""
    n_tickets: int
    total_comparisons: int
    generation_time_seconds: float
    model_preset: str
    include_files: bool
    validation: ValidationResult


@dataclass
class SimilarityMatrix:
    """Complete similarity matrix with metadata."""
    matrix: Dict[str, Dict[str, float]]
    metadata: SimilarityMatrixMetadata
    ticket_keys: List[str]

    def get_similarity(self, key_a: str, key_b: str) -> Optional[float]:
        """Get similarity score between two tickets."""
        return self.matrix.get(key_a, {}).get(key_b)


@dataclass
class MatrixValidationResult:
    """Result of similarity matrix validation."""
    is_valid: bool
    self_comparison_scores: List[float]
    min_self_score: float
    max_self_score: float
    avg_self_score: float
    symmetry_violations: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class LLMSimilarityResponse:
    """Structured response from LLM similarity comparison."""
    similarity_score: float
    reasoning: Optional[str] = None
    confidence: Optional[float] = None
    factors_considered: List[str] = field(default_factory=list)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMSimilarityResponse':
        """Create from dictionary response."""
        return cls(
            similarity_score=float(data["similarity_score"]),
            reasoning=data.get("reasoning"),
            confidence=data.get("confidence"),
            factors_considered=data.get("factors_considered", [])
        )


@dataclass
class LLMRefinementResponse:
    """Structured response from LLM idea refinement."""
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    confidence: Optional[float] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMRefinementResponse':
        """Create from dictionary response."""
        return cls(
            title=data["title"],
            business_value=data["business_value"],
            description=data["description"],
            acceptance_criteria=data.get("acceptance_criteria", []),
            open_questions=data.get("open_questions", []),
            suggested_design=data.get("suggested_design", ""),
            likely_files=data.get("likely_files", []),
            impact_assessment=data.get("impact_assessment", ""),
            confidence=data.get("confidence")
        )
